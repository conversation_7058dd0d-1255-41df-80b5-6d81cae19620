<template>
  <svg
    :width="sizePx"
    :height="sizePx"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    aria-hidden="true"
    focusable="false"
    class="play-icon"
  >
    <!-- 居中三角（视觉居中）：左顶点在 x=5，右顶点在 x=12，垂直居中 y=8 -->
    <polygon points="5,3 5,13 12,8" />
  </svg>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{ size?: number | string }>(), { size: 12 })
const sizePx = typeof props.size === 'number' ? `${props.size}px` : props.size
</script>

<style scoped>
.play-icon { display: inline-block; vertical-align: middle; }
</style>
