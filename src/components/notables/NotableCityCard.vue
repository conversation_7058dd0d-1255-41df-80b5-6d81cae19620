<template>
  <section class="city-card warm-card">
    <div class="header">
      <div class="left">
        <span class="city">{{ city }}</span>
        <button class="icon-btn" title="语音播放"><PlayIcon :size="12" /></button>
        <button class="icon-btn wand" title="小魔仙">✨</button>
      </div>
      <div class="right">
        <button class="icon-btn pale" title="详情" aria-label="查看详情" @click="onDetails">📃</button>
        <button class="icon-btn pale" title="复制" aria-label="复制内容" @click="onCopy">📋</button>
        <button class="icon-btn pale" title="转发" aria-label="系统分享" @click="onShare">📤</button>
      </div>
    </div>
    <div class="body">
      <ul class="list">
        <li v-for="(t, i) in notices" :key="i" class="item">{{ t }}</li>
      </ul>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useShare } from '@/composables/useShare'
import PlayIcon from '../common/playicon.vue'

const { copyWithToast, shareWithFallback, showDetailsPlaceholder } = useShare()

interface IProps {
  city: string
  notices: string[]
}

const props = defineProps<IProps>()

function buildShareText() {
  const lines = props.notices && props.notices.length ? props.notices.join('\n') : ''
  return `【划重点】${props.city}\n${lines}`
}

function onDetails() { showDetailsPlaceholder() }
async function onCopy() { await copyWithToast(buildShareText()) }
async function onShare() { await shareWithFallback(`划重点 - ${props.city}`, buildShareText()) }
</script>

<style scoped>
.city-card { padding: 20px; border:2px solid var(--border-accent); border-radius:20px; background: var(--bg-glass); backdrop-filter: blur(10px); overflow: hidden; transition: all 0.3s ease; }
.city-card:hover { background: var(--bg-glass-hover); transform: translateY(-1px); }
.header { display: flex; align-items: center; gap:12px; padding:12px 16px; border-radius:16px; background: var(--bg-glass); backdrop-filter: blur(10px); }
.left { display:flex; align-items:center; gap:6px; }
.right { margin-left:auto; display:flex; gap:6px; }
.city { font-size: 16px; font-weight: 700; color: #000000; }
.icon-btn { width:20px; height:20px; border-radius:10px; border:1px solid rgba(0,0,0,0.06); background: rgba(255,255,255,0.9); color:#2e7d32; display:inline-flex; align-items:center; justify-content:center; -webkit-appearance: none; appearance: none; line-height: 0; }
.icon-btn .play-icon { display:block; fill: currentColor; }
.icon-btn.pale { color:#607d8b }
.icon-btn.wand { color:#DAA520 }
.body { margin-top:8px; padding:12px 16px; border-radius:16px; background: var(--bg-glass); backdrop-filter: blur(10px); }
.list { margin: 0; padding: 0; display: flex; flex-direction: column; gap: 4px; list-style:none; }
.item { font-size: 14px; color: #000000; line-height: 1.5; padding: 4px 0; }

/* 简易图标占位（播放图标改为 PlayIcon 组件） */
.i-wand { width:12px; height:12px; display:inline-block; position:relative; background: transparent }
.i-wand::before { content:""; position:absolute; left:5px; top:1px; width:2px; height:10px; background: currentColor; transform: rotate(28deg); border-radius:2px }
.i-wand::after { content:""; position:absolute; left:2px; top:0; width:4px; height:4px; border-radius:50%; background: currentColor; opacity:.9 }
</style>
