// WeatherService（Web）：聚合多城市天气数据，接口尽量对齐 iOS WeatherService
// 约束：严格复用 adapters/weatherAdapter.ts，不新增 iOS 没有的功能。
// 说明：当前适配层仅实现了 实况/7天预报；逐时/分钟级/预警留空位以便后续接入。

import {
  fetchCurrentWeather,
  fetchWeatherForecast7d,
  fetchWeatherWarnings,
  fetchAirQuality,
  fetchHourlyWeather,
  getLocationIdForCity,
  type ICurrentWeather,
  type IDailyWeather,
  type IWeatherWarning,
  type IAirQuality,
  type IHourlyWeather,
} from '@/adapters/weatherAdapter'
import { cityResolver } from './CityResolver'

export interface ILocationWeatherData {
  locationId: string
  cityName?: string
  currentWeather: ICurrentWeather | null
  weatherForecast: IDailyWeather[]
  hourlyWeather?: IHourlyWeather[] | null
  minutelyWeather?: unknown[] | null
  minutelySummary?: string | null
  weatherWarnings?: IWeatherWarning[] | null
  airQuality?: IAirQuality | null
  updatedAt: number // ms
}

// Type aliases for compatibility
export type LocationWeatherData = ILocationWeatherData
export type WeatherWarning = IWeatherWarning
export type AirQuality = IAirQuality
export type HourlyWeather = IHourlyWeather

const DEFAULT_TTL_MS = 60 * 1000

export class WeatherService {
  private store: Record<string, ILocationWeatherData> = {}

  private ttl = DEFAULT_TTL_MS

  public queriedCities: string[] = [] // 来自备忘录抽取的“当前关注城市”（显示/计算范围）

  constructor(opts?: { ttlMs?: number }) {
    if (opts?.ttlMs) this.ttl = opts.ttlMs
  }

  // 与 iOS 命名保持一致
  getLocationIdForCity(cityName: string): string | undefined {
    return getLocationIdForCity(cityName)
  }

  // 异步解析：优先 Top100，本地未命中则懒加载 /poi/cities.json
  async resolveLocationIdForCity(cityName: string): Promise<string | undefined> {
    // 先尝试同步 Top100
    const syncId = cityResolver.getSync(cityName)
    if (syncId) return syncId
    // 再尝试懒加载大表
    return cityResolver.get(cityName)
  }

  // iOS 用于显示更完整城市名；Web 端暂无反查映射，先返回 undefined
  getDetailedCityNameFromLocationId(_id: string): string | undefined {
    return undefined
  }

  setQueriedCities(cities: string[]) {
    this.queriedCities = Array.from(new Set(cities.filter(Boolean)))
  }

  getCityBundleFromCache(locationId: string): ILocationWeatherData | null {
    const item = this.store[locationId]
    if (!item) return null
    const fresh = Date.now() - item.updatedAt < this.ttl
    return fresh ? item : null
  }

  async fetchCityBundle(locationId: string, displayName?: string): Promise<LocationWeatherData> {
    const cached = this.getCityBundleFromCache(locationId)
    if (cached) return cached

    const [current, forecast, warnings, air, hourly] = await Promise.all([
      fetchCurrentWeather(locationId),
      fetchWeatherForecast7d(locationId),
      fetchWeatherWarnings(locationId).catch(() => [] as WeatherWarning[]),
      fetchAirQuality(locationId).catch(() => null as AirQuality | null),
      fetchHourlyWeather(locationId).catch(() => [] as HourlyWeather[]),
    ])

    const bundle: LocationWeatherData = {
      locationId,
      cityName: displayName,
      currentWeather: current,
      weatherForecast: forecast ?? [],
      hourlyWeather: hourly ?? null,
      minutelyWeather: null,
      minutelySummary: null,
      weatherWarnings: warnings ?? null,
      airQuality: air ?? null,
      updatedAt: Date.now(),
    }
    this.store[locationId] = bundle
    return bundle
  }

  async fetchAllByCities(cityNames: string[]): Promise<Record<string, LocationWeatherData>> {
    const results: Record<string, LocationWeatherData> = {}
    await Promise.all(
      cityNames.map(async (name) => {
        const id = (await this.resolveLocationIdForCity(name)) || this.getLocationIdForCity(name)
        if (!id) return
        const displayName = this.getDetailedCityNameFromLocationId(id) || name
        const bundle = await this.fetchCityBundle(id, displayName)
        results[id] = bundle
      })
    )
    return results
  }

  getStore(): Record<string, LocationWeatherData> {
    return this.store
  }

  // 清空缓存与查询城市列表（用于备忘录被清空时同步 UI 状态）
  clear() {
    this.store = {}
    this.queriedCities = []
  }
}
