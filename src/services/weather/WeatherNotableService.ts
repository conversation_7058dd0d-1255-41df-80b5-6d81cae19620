// WeatherNotableService（Web）——复刻 iOS 划重点服务的结构/流程/阈值
// 约束：不新增 iOS 没有的功能；受限于当前适配层字段，部分检测器先占位。
// 文案与阈值：按 iOS 逻辑对齐（例如体感差≥3℃、湿度≥80%/≤30%、今日温差等）。

import type { ICurrentWeather, IDailyWeather } from '@/adapters/weatherAdapter'
import type { ILocationWeatherData, LocationWeatherData, WeatherService } from './WeatherService'

export type NotableCategory =
  | 'current'
  | 'seasonal'
  | 'health'
  | 'forecast'
  | 'hourly'
  | 'warning'

export interface IWeatherNotable {
  locationId: string
  cityName: string
  notice: string
  priority: number // 数字越小优先级越高
  category: NotableCategory
}

const DEBOUNCE_MS = 1000
const CACHE_MS = 60 * 1000
const NOTICE_TEXT_CACHE_MS = 5000

export class WeatherNotableService {
  private isProcessing = false

  private notables: IWeatherNotable[] = []

  private lastHash = ''

  private lastUpdatedAt = 0

  private debounceTimer: ReturnType<typeof setTimeout> | null = null

  private pendingResolve: (() => void) | null = null

  private pendingPromise: Promise<void> | null = null

  private debugLogs: string[] = []

  // 文本缓存（5s）
  private noticesCache: { texts: string[]; ts: number } | null = null

  // 对外：清空结果与缓存（用于备忘录清空时同步 UI）
  clear() {
    this.isProcessing = false
    this.notables = []
    this.lastHash = ''
    this.lastUpdatedAt = 0
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = null
    }
    this.pendingResolve = null
    this.pendingPromise = null
    this.debugLogs = []
    this.noticesCache = null
  }

  refreshNotables(weatherService: WeatherService): Promise<void> {
    if (this.debounceTimer) clearTimeout(this.debounceTimer)
    if (!this.pendingPromise) {
      this.pendingPromise = new Promise<void>((resolve) => {
        this.pendingResolve = resolve
      })
    }
    this.debounceTimer = setTimeout(async () => {
      await this.performRefresh(weatherService)
      // 仅在当前批次完成时 resolve 一次
      if (this.pendingResolve) {
        this.pendingResolve()
        this.pendingResolve = null
        this.pendingPromise = null
      }
    }, DEBOUNCE_MS)
    return this.pendingPromise
  }

  private isCacheValid(currentHash: string): boolean {
    const fresh = Date.now() - this.lastUpdatedAt < CACHE_MS
    return fresh && this.lastHash === currentHash
  }

  private buildHash(ws: WeatherService): string {
    const keys = ws.queriedCities.join('|')
    const parts: string[] = [keys]
    const store = ws.getStore()
    // eslint-disable-next-line no-restricted-syntax
    for (const id of Object.keys(store).sort()) {
      const b = store[id]
      const now = b.currentWeather
      const today = (b.weatherForecast && b.weatherForecast[0]) || null
      parts.push(
        [
          id,
          now?.temp ?? '',
          now?.feelsLike ?? '',
          now?.humidity ?? '',
          today?.tempMax ?? '',
          today?.tempMin ?? '',
        ].join(',')
      )
    }
    return parts.join('#')
  }

  private async performRefresh(weatherService: WeatherService) {
    if (this.isProcessing) return
    this.isProcessing = true

    try {
      const hash = this.buildHash(weatherService)
      this.log(`[Notables] start hash=${hash}`)
      if (this.isCacheValid(hash)) {
        this.log('[Notables] cache hit, skip recompute')
        this.isProcessing = false
        return
      }

      const store = weatherService.getStore()
      const locationIds = Object.keys(store)
      this.log(`[Notables] cities=${weatherService.queriedCities.join(',')} ids=${locationIds.join(',')}`)

      const all: IWeatherNotable[] = []
      await Promise.all(
        locationIds.map((locId) => {
          const bundle = store[locId]
          const cityName = bundle.cityName || locId
          const cityNotables = this.detectForCity(bundle, cityName)
          all.push(...cityNotables)
          this.log(`[Notables] ${cityName} produced ${cityNotables.length} items`)
          return Promise.resolve()
        })
      )

      const sorted = this.sortNotables(all)
      const ensured = this.ensureMinimumNotables(sorted, store)

      this.notables = ensured
      this.lastHash = hash
      this.lastUpdatedAt = Date.now()
      this.noticesCache = null // 失效文本缓存
      this.log(`[Notables] total=${ensured.length}`)
    } finally {
      this.isProcessing = false
    }
  }

  private log(s: string) {
    const ts = new Date().toISOString()
    // 控制长度避免无限增长
    if (this.debugLogs.length > 400) this.debugLogs.splice(0, this.debugLogs.length - 300)
    this.debugLogs.push(`[${ts}] ${s}`)
  }

  getDebugLogs(): string[] {
    return [...this.debugLogs]
  }

  private detectForCity(bundle: LocationWeatherData, cityName: string): IWeatherNotable[] {
    const res: IWeatherNotable[] = []
    res.push(
      ...this.detectCurrent(bundle.locationId, cityName, bundle.currentWeather),
      ...this.detectForecast(bundle.locationId, cityName, bundle.weatherForecast),
      ...this.detectSeasonal(bundle.locationId, cityName, bundle.weatherForecast),
      ...this.detectHealth(bundle.locationId, cityName, bundle.currentWeather, bundle.weatherForecast),
      ...this.detectHourly(bundle.locationId, cityName, (bundle as { hourlyWeather?: unknown[] }).hourlyWeather || null),
      ...this.detectWarning(bundle.locationId, cityName, (bundle as { weatherWarnings?: unknown[] }).weatherWarnings || null)
    )
    return res
  }

  // 当前天气类（示例：体感温度差、湿度阈值）
  private detectCurrent(locationId: string, cityName: string, now: ICurrentWeather | null): IWeatherNotable[] {
    const out: IWeatherNotable[] = []
    if (!now) return out

    const temp = toNum(now.temp)
    const feels = toNum(now.feelsLike)
    const hum = toNum(now.humidity)

    // 体感温差阈值：≥3℃
    if (Number.isFinite(temp) && Number.isFinite(feels)) {
      const diff = Math.abs(feels - temp)
      if (diff >= 3) {
        const hotter = feels > temp
        const text = hotter
          ? `${cityName}当前体感比实际高约${round0(diff)}℃，注意降温防汗`
          : `${cityName}当前体感比实际低约${round0(diff)}℃，注意保暖防风`
        out.push({ locationId, cityName, notice: text, priority: 2, category: 'current' })
      }
    }

    // 湿度：≥80% 或 ≤30%
    if (Number.isFinite(hum)) {
      if (hum >= 80) {
        out.push({
          locationId,
          cityName,
          notice: `${cityName}湿度偏高（${round0(hum)}%），闷热感明显，注意通风与防潮`,
          priority: 3,
          category: 'current',
        })
      } else if (hum <= 30) {
        out.push({
          locationId,
          cityName,
          notice: `${cityName}空气偏干（${round0(hum)}%），注意补水与润喉`,
          priority: 3,
          category: 'current',
        })
      }
    }

    return out
  }

  // 季节类：占位，待按 iOS 规则精确实现（例如入秋/入冬提示等）。
  private detectSeasonal(
    locationId: string,
    cityName: string,
    daily: IDailyWeather[]
  ): IWeatherNotable[] {
    const out: IWeatherNotable[] = []
    // TODO(iOS parity): 结合日期/昼夜温差/连续天数等规则，复刻 iOS 季节文案与阈值
    return out
  }

  // 健康类：占位，待按 iOS 规则精确实现（例如穿衣、感冒、紫外线等）。
  private detectHealth(
    locationId: string,
    cityName: string,
    now: ICurrentWeather | null,
    daily: IDailyWeather[]
  ): IWeatherNotable[] {
    const out: IWeatherNotable[] = []
    // TODO(iOS parity): 结合体感、湿度、最高/最低温、风力等，复刻 iOS 健康相关提示
    return out
  }

  // 逐时类：占位；当前适配层无逐时数据，返回空。
  private detectHourly(
    locationId: string,
    cityName: string,
    hourly: unknown[] | null
  ): IWeatherNotable[] {
    const out: IWeatherNotable[] = []
    // TODO(iOS parity): 待接入 hourlyWeather 后，按 iOS 阈值检测未来数小时的降水/温度/风力等
    return out
  }

  // 预警类：占位；当前适配层无正式预警数据，返回空。
  private detectWarning(
    locationId: string,
    cityName: string,
    warnings: unknown[] | null
  ): IWeatherNotable[] {
    const out: IWeatherNotable[] = []
    // TODO(iOS parity): 待接入 weatherWarnings 后，复刻 iOS 预警级别/类型与文案
    return out
  }

  // 预报类（示例：今日温差/最高温提示）
  private detectForecast(locationId: string, cityName: string, daily: IDailyWeather[]): IWeatherNotable[] {
    const out: IWeatherNotable[] = []
    if (!daily || daily.length === 0) return out

    const today = daily[0]
    const tmax = toNum(today.tempMax)
    const tmin = toNum(today.tempMin)

    if (Number.isFinite(tmax) && Number.isFinite(tmin)) {
      const span = tmax - tmin
      if (span >= 8) {
        out.push({
          locationId,
          cityName,
          notice: `${cityName}今日温差较大（${round0(tmin)}~${round0(tmax)}℃），外出注意增减衣物`,
          priority: 4,
          category: 'forecast',
        })
      }
    }

    return out
  }

  private sortNotables(list: IWeatherNotable[]): IWeatherNotable[] {
    return list.sort((a, b) => {
      if (a.priority !== b.priority) return a.priority - b.priority
      if (a.cityName !== b.cityName) return a.cityName.localeCompare(b.cityName, 'zh')
      return a.notice.length - b.notice.length
    })
  }

  private ensureMinimumNotables(list: IWeatherNotable[], store: Record<string, LocationWeatherData>): IWeatherNotable[] {
    if (list.length >= 3) return list
    const out = [...list]

    // 兜底：用实况与今日预报补足到≥3条
    const ids = Object.keys(store)
    // eslint-disable-next-line no-restricted-syntax
    for (const id of ids) {
      if (out.length >= 3) break
      const b = store[id]
      const city = b.cityName || id
      const now = b.currentWeather
      const daily = b.weatherForecast

      if (now && out.length < 3) {
        const feels = toNum(now.feelsLike)
        const temp = toNum(now.temp)
        if (Number.isFinite(temp) && Number.isFinite(feels)) {
          out.push({
            locationId: id,
            cityName: city,
            notice: `${city}当前${round0(temp)}℃（体感${round0(feels)}℃）`,
            priority: 9,
            category: 'current',
          })
        }
      }

      if (daily && daily.length > 0 && out.length < 3) {
        const tmax = toNum(daily[0].tempMax)
        const tmin = toNum(daily[0].tempMin)
        if (Number.isFinite(tmax) && Number.isFinite(tmin)) {
          out.push({
            locationId: id,
            cityName: city,
            notice: `${city}今日${round0(tmin)}~${round0(tmax)}℃`,
            priority: 9,
            category: 'forecast',
          })
        }
      }
    }

    return out
  }

  // 对外：获取文本列表（带5s缓存）
  get weatherNotices(): string[] {
    const now = Date.now()
    if (this.noticesCache && now - this.noticesCache.ts < NOTICE_TEXT_CACHE_MS) {
      return this.noticesCache.texts
    }
    const texts = this.notables.map((n) => n.notice)
    this.noticesCache = { texts, ts: now }
    return texts
  }

  // 对外：按城市分组（用于 NotablesView 展示）
  get groupedNotices(): Array<{ city: string; notices: string[] }> {
    const groups: Record<string, string[]> = {}
    this.notables.forEach(n => {
      if (!groups[n.cityName]) groups[n.cityName] = []
      groups[n.cityName].push(n.notice)
    })
    return Object.keys(groups).map((city) => ({ city, notices: groups[city] }))
  }
}

function toNum(s: string | number | undefined | null): number {
  if (s == null) return NaN
  if (typeof s === 'number') return s
  const n = Number(String(s).replace(/[^\d.+-]/g, ''))
  return Number.isFinite(n) ? n : NaN
}

function round0(n: number): number {
  return Math.round(n)
}
